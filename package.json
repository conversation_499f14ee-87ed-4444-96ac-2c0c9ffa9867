{"name": "session1", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "watch": "ng build --watch --configuration development", "test": "ng test", "serve:ssr:session1": "node dist/session1/server/server.mjs"}, "prettier": {"overrides": [{"files": "*.html", "options": {"parser": "angular"}}]}, "private": true, "dependencies": {"@angular/common": "^20.1.0", "@angular/compiler": "^20.1.0", "@angular/core": "^20.1.0", "@angular/forms": "^20.1.0", "@angular/platform-browser": "^20.1.0", "@angular/platform-server": "^20.1.0", "@angular/router": "^20.1.0", "@angular/ssr": "^20.1.0", "@fortawesome/fontawesome-free": "^6.7.2", "bootstrap": "^5.3.7", "express": "^5.1.0", "rxjs": "~7.8.0", "tslib": "^2.3.0"}, "devDependencies": {"@angular/build": "^20.1.0", "@angular/cli": "^20.1.0", "@angular/compiler-cli": "^20.1.0", "@types/express": "^5.0.1", "@types/jasmine": "~5.1.0", "@types/node": "^20.17.19", "jasmine-core": "~5.8.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "typescript": "~5.8.2"}}